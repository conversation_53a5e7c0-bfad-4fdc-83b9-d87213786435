package com.digiwin.escloud.commentservice.mapper.customerservice;

import com.digiwin.escloud.commentservice.model.*;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;


@Mapper
public interface IEvaluationManagerMapper {

    @Select("select count(*) from evaluation_comment")
    public Integer countEvaluationComment2();

    @Select("select * from evaluation_summary where evaluation_category_id = #{categoryId} and evaluation_object_id=#{objectId} LIMIT 1")
    @Results(value = {
            @Result(property="categoryId", column="evaluation_category_id"),
            @Result(property="objectId", column="evaluation_object_id"),
            @Result(property="gradeAvg", column="evaluation_grade_avg"),
            @Result(property="gradeTotalCount", column="evaluation_garde_total_count"),
            @Result(property="upvoteTotalCount", column="evaluation_upvote_total_count"),
            @Result(property="commentTotalCount", column="evaluation_comment_total_count"),
            @Result(property="__version__", column="__version__")})
    public EvaluateSummary getEvaluationSummary(@Param("categoryId") int categoryId,
                                                @Param("objectId")String objectId);

    @Select("select c.*,u.name as nickname from evaluation_comment c join mars_userpersonalinfo u on c.evaluation_comment_user_id = u.userid where c.evaluation_category_id = #{categoryId} and c.evaluation_object_id=#{objectId} and c.is_hidden = 0")
    @Results(value = {
            @Result(property="commentId", column="evaluation_comment_id"),
            @Result(property="categoryId", column="evaluation_category_id"),
            @Result(property="objectId", column="evaluation_object_id"),
            @Result(property="commentUserId", column="evaluation_comment_user_id"),
            @Result(property="commentTime", column="evaluation_comment_time"),
            @Result(property="commentContent", column="evaluation_comment_content"),
            @Result(property="parentId", column="parent_id"),
            @Result(property="isHidden", column="is_hidden"),
            @Result(property="commentUserNickName", column="nickname"),
            @Result(property="__version__", column="__version__")})
    public List<EvaluateComment2> getAllComments(@Param("categoryId") int categoryId,
                                                 @Param("objectId")String objectId);

    @Insert("insert into evaluation_upvote (evaluation_category_id,evaluation_object_id,evaluation_upvote_user_id,evaluation_upvote_time)values"+
            "(#{categoryId},#{objectId},#{userId},NOW())")
    public Integer insertUpvote(@Param("userId")String userId,
                                 @Param("categoryId") int categoryId,
                                 @Param("objectId")String objectId);

    @Insert("INSERT INTO evaluation_summary (evaluation_category_id,evaluation_object_id,evaluation_upvote_total_count) "+
            "VALUES (#{categoryId},#{objectId},(SELECT COUNT(*) from evaluation_upvote where evaluation_category_id=#{categoryId} and evaluation_object_id=#{objectId})) "+
            " ON DUPLICATE KEY UPDATE evaluation_upvote_total_count=(SELECT COUNT(*) from evaluation_upvote where evaluation_category_id=#{categoryId} and evaluation_object_id=#{objectId})")
    public Integer UpdateUpvoteSummary(@Param("objectId")String objectId,
                                @Param("categoryId") int categoryId);

    @Insert("insert into evaluation_comment (evaluation_comment_id,evaluation_category_id,evaluation_object_id,evaluation_comment_user_id,evaluation_comment_time,evaluation_comment_content,parent_id)values"+
            "(#{commentId},#{categoryId},#{objectId},#{userId},NOW(),#{content},#{parentId})")
    public Integer insertComment(@Param("commentId")String commentId,
                                 @Param("userId")String userId,
                                 @Param("categoryId") int categoryId,
                                 @Param("objectId") String objectId,
                                 @Param("content") String content,
                                 @Param("parentId") String parentId);

    @Insert("insert into evaluation_comment (evaluation_comment_id,evaluation_category_id,evaluation_object_id,evaluation_comment_user_id,evaluation_comment_time,evaluation_comment_content,parent_id,sid,eid,dataSource)values"+
            "(#{commentId},#{categoryId},#{objectId},#{userId},NOW(),#{content},#{parentId},#{sid},#{eid},#{dataSource})")
    public Integer insertCommentNew(@Param("commentId")String commentId,
                                 @Param("userId")String userId,
                                 @Param("categoryId") int categoryId,
                                 @Param("objectId") String objectId,
                                 @Param("content") String content,
                                 @Param("parentId") String parentId,
                                 @Param("sid") long sid,
                                 @Param("eid") long eid,
                                 @Param("dataSource") String dataSource);
    @Insert("INSERT INTO evaluation_summary (evaluation_category_id,evaluation_object_id,evaluation_comment_total_count) "+
            "VALUES (#{categoryId},#{objectId},(SELECT COUNT(*) from evaluation_comment where evaluation_category_id=#{categoryId} and evaluation_object_id=#{objectId})) "+
            " ON DUPLICATE KEY UPDATE evaluation_comment_total_count=(SELECT COUNT(*) from evaluation_comment where evaluation_category_id=#{categoryId} and evaluation_object_id=#{objectId})")
    public Integer UpdateCommentSummary(@Param("objectId")String objectId,
                                       @Param("categoryId") int categoryId);

    @Insert("insert into evaluation_grade (evaluation_category_id,evaluation_object_id,evaluation_grade_time,evaluation_grade_user_id,evaluation_grade_value) values"+
            "(#{categoryId},#{objectId},NOW(),#{userId},#{grade})"+
            " ON DUPLICATE KEY UPDATE evaluation_grade_value = #{grade}")
    public Integer insertGrade(@Param("userId")String userId,
                                 @Param("categoryId") int categoryId,
                                 @Param("objectId") String objectId,
                                 @Param("grade") Double grade);
    @Insert("INSERT INTO evaluation_summary (evaluation_category_id,evaluation_object_id,evaluation_grade_avg,evaluation_garde_total_count) " +
            "VALUES (#{categoryId}," +
            "#{objectId}," +
            "(SELECT AVG(evaluation_grade_value) from evaluation_grade where evaluation_category_id=#{categoryId} and evaluation_object_id=#{objectId})," +
            "(SELECT COUNT(*) from evaluation_grade where evaluation_category_id=#{categoryId} and evaluation_object_id=#{objectId})" +
            ") " +
            "ON DUPLICATE KEY UPDATE " +
            "evaluation_grade_avg=(SELECT AVG(evaluation_grade_value) from evaluation_grade where evaluation_category_id=#{categoryId} and evaluation_object_id=#{objectId}),\n" +
            "evaluation_garde_total_count=(SELECT COUNT(*) from evaluation_grade where evaluation_category_id=#{categoryId} and evaluation_object_id=#{objectId})")
    public Integer UpdateGradeSummary(@Param("objectId")String objectId,
                                        @Param("categoryId") int categoryId);

    @Select("select (case count(*) when 0 then 0 else 1 end) as isGraded,g.evaluation_grade_value from evaluation_grade g where g.evaluation_category_id = #{categoryId} and g.evaluation_object_id=#{objectId} and g.evaluation_grade_user_id = #{userId}")
    @Results(value = {
            @Result(property="isGraded", column="isGraded"),
            @Result(property="grade", column="evaluation_grade_value")})
    public EvaluatePrivateStatus getGrade(@Param("userId")String userId,
                                          @Param("categoryId") int categoryId,
                                          @Param("objectId") String objectId);

    @Select("select count(*) from evaluation_upvote g where g.evaluation_category_id = #{categoryId} and g.evaluation_object_id=#{objectId} and g.evaluation_upvote_user_id = #{userId}")
    public Integer countEvaluationUpvote(@Param("userId")String userId,
                                         @Param("categoryId") int categoryId,
                                         @Param("objectId") String objectId);

    @Select("select count(*) from evaluation_comment g where g.evaluation_category_id = #{categoryId} and g.evaluation_object_id=#{objectId} and g.evaluation_comment_user_id = #{userId}")
    public Integer countEvaluationComment(@Param("userId")String userId,
                                         @Param("categoryId") int categoryId,
                                         @Param("objectId") String objectId);

    @Select("select category.evaluation_category_id as evaluation_category_id, #{evaluation_object_id} as evaluation_object_id, \n" +
            "    DATE_FORMAT(grade.evaluation_grade_time,'%Y-%m-%d') as evaluation_grade_time, \n" +
            "   grade.evaluation_grade_user_id as evaluation_grade_user_id, grade.evaluation_grade_value \n" +
            "from evaluation_category category\n" +
            "left join evaluation_grade grade on grade.evaluation_category_id = category.evaluation_category_id and grade.evaluation_object_id = #{evaluation_object_id} \n" +
            "          and #{evaluation_grade_user_id} in (grade.evaluation_grade_user_id, '')  \n" +
            "where category.evaluation_category_id = #{evaluation_category_id}  ")
    @Results(value = {
            @Result(property="evaluation_category_id", column="evaluation_category_id"),
            @Result(property="evaluation_object_id", column="evaluation_object_id"),
            @Result(property="evaluation_grade_time", column="evaluation_grade_time"),
            @Result(property="evaluation_grade_user_id", column="evaluation_grade_user_id"),
            @Result(property="evaluation_grade_value", column="evaluation_grade_value"),
            @Result(property="evaluationGradeItems", javaType=List.class, column="{evaluation_category_id=evaluation_category_id,evaluation_object_id=evaluation_object_id,evaluation_grade_user_id=evaluation_grade_user_id}",
                    many = @Many(select = "GetEvaluationGradeItemsByObject")),
            @Result(property="evaluateComments", javaType=List.class, column="{evaluation_category_id=evaluation_category_id,evaluation_object_id=evaluation_object_id}",
                    many = @Many(select = "getComments"))})
    public EvaluationGrade GetEvaluationGradeByObject(@Param("evaluation_category_id")int evaluation_category_id,
                                                      @Param("evaluation_object_id") String evaluation_object_id,
                                                      @Param("evaluation_grade_user_id") String evaluation_grade_user_id);

    @Select("select setting.evaluation_item_id, item.evaluation_item_name, \n" +
            "       grade_item.evaluation_grade_value,  grade_item.evaluation_grade_user_id as evaluation_grade_user_id, \n" +
            "       DATE_FORMAT(grade_item.evaluation_grade_time,'%Y-%m-%d') as evaluation_grade_time\n" +
            "from evaluation_item_setting setting\n" +
            "LEFT JOIN evaluation_item item on item.evaluation_item_id = setting.evaluation_item_id\n" +
            "LEFT JOIN evaluation_grade_item grade_item on grade_item.evaluation_category_id =setting.evaluation_category_id \n" +
            "                                          and grade_item.evaluation_object_id =  #{evaluation_object_id}\n" +
            "                                          and grade_item.evaluation_item_id = item.evaluation_item_id\n" +
            "                                          and #{evaluation_grade_user_id} in (grade_item.evaluation_grade_user_id,'')\n" +
            "where setting.evaluation_category_id =  #{evaluation_category_id}  ")
    @Results(value = {
            @Result(property="evaluation_item_id", column="evaluation_item_id"),
            @Result(property="evaluation_item_name", column="evaluation_item_name"),
            @Result(property="evaluation_grade_value", column="evaluation_grade_value"),
            @Result(property="evaluation_grade_user_id", column="evaluation_grade_user_id"),
            @Result(property="evaluation_grade_time", column="evaluation_grade_time")})
    public List<EvaluationGradeItem> GetEvaluationGradeItemsByObject(@Param("evaluation_category_id")int evaluation_category_id,
                                                      @Param("evaluation_object_id") String evaluation_object_id,
                                                      @Param("evaluation_grade_user_id") String evaluation_grade_user_id);

    @Select("select c.*,u.name as nickname from evaluation_comment c join mars_userpersonalinfo u on c.evaluation_comment_user_id = u.userid where c.evaluation_category_id = #{evaluation_category_id} and c.evaluation_object_id=#{evaluation_object_id} and c.is_hidden = 0")
    @Results(value = {
            @Result(property="commentId", column="evaluation_comment_id"),
            @Result(property="categoryId", column="evaluation_category_id"),
            @Result(property="objectId", column="evaluation_object_id"),
            @Result(property="commentUserId", column="evaluation_comment_user_id"),
            @Result(property="commentTime", column="evaluation_comment_time"),
            @Result(property="commentContent", column="evaluation_comment_content"),
            @Result(property="parentId", column="parent_id"),
            @Result(property="isHidden", column="is_hidden"),
            @Result(property="commentUserNickName", column="nickname"),
            @Result(property="__version__", column="__version__")})
    public List<EvaluateComment2> getComments(@Param("evaluation_category_id") int evaluation_category_id,
                                                 @Param("evaluation_object_id")String evaluation_object_id);

    @Insert("INSERT INTO evaluation_grade (sid,eid,dataSource, evaluation_category_id,evaluation_object_id,\n" +
            "        evaluation_grade_time,evaluation_grade_user_id,evaluation_grade_value)\n" +
            "VALUES (#{sid},#{eid},#{dataSource},#{categoryId},#{objectId},NOW(),#{userId},#{grade})\n" +
            "ON DUPLICATE KEY UPDATE evaluation_grade_time = NOW(),evaluation_grade_value = #{grade} ")
    public Integer insertEvaluationGrade(@Param("sid") long sid,
                                         @Param("eid") long eid,
                                         @Param("dataSource") String dataSource,
                                         @Param("categoryId") int categoryId,
                                         @Param("objectId") String objectId,
                                         @Param("userId")String userId,
                                         @Param("grade")double grade);

    @Insert("INSERT INTO evaluation_grade_item(evaluation_category_id,evaluation_object_id,evaluation_item_id,\n" +
            "evaluation_grade_value,evaluation_grade_user_id,evaluation_grade_time) \n" +
            "VALUES (#{categoryId},#{objectId},#{itemId},#{grade},#{userId},NOW())\n" +
            "ON DUPLICATE KEY UPDATE evaluation_grade_time = NOW(),evaluation_grade_value = #{grade} ")
    public Integer insertEvaluationGradeItem(@Param("categoryId") int categoryId,
                                         @Param("objectId") String objectId,
                                         @Param("itemId")int itemId,
                                         @Param("userId")String userId,
                                         @Param("grade")double grade);

    @Select("select ifnull(cs.email,'') email \n" +
            "from issue issue \n" +
            "left JOIN mars_customerservicestaffsetting cs on cs.departmentcode = issue.ServiceDepartment and cs.productCode = issue.ProductCode \n" +
            "where issue.IssueId = #{issueId} and cs.isIssueEvaluationNoticed = TRUE")
    public List<String> SelectEmail(@Param("issueId")String issueId);
    @Select("select c.CustomerName, cs.fullname, issue.CrmId\n" +
            "from issue \n" +
            "LEFT JOIN mars_customer c on c.CustomerServiceCode = issue.ServiceCode\n" +
            "LEFT JOIN mars_staffaccount s on s.userid = if( issue.ProductCode IN( '100','06','164') , issue.main_charge , issue.ServiceId)\n" +
            "LEFT JOIN mars_customerservicesatff cs on cs.itcode = s.itcode\n" +
            "where issue.IssueId =  #{issueId} \n" +
            "LIMIT 0,1")
    @Results(value = {
            @Result(property="customerName", column="CustomerName"),
            @Result(property="fullname", column="fullname"),
            @Result(property="crmid", column="CrmId")})
    public IssueBasicInfo SelectIssueInfo(@Param("issueId")String issueId);

    @Select("select cs.`language`\n" +
            "from issue issue\n" +
            "INNER JOIN mars_userpersonalinfo cs on cs.userid = issue.ServiceId \n" +
            "where issue.IssueId = #{issueId} \n" +
            "limit 0,1")
    public String SelectLanguage(@Param("issueId")String issueId);

    @Select("select  operation.programVersion \n" +
            "from im_operation_record operation \n" +
            "where  operation.UserId = #{UserId} \n" +
            "order by operation.Id desc \n"+
            "limit 0,1")
    public String SelectVersion(@Param("UserId")String UserId);

    @Select("select count(*) from evaluation_grade where evaluation_category_id=  #{categoryId}  and evaluation_object_id = #{objectId}  and evaluation_grade_value= #{gradeValue} and (#{sid}=null or #{sid}=sid)")
    public Integer SelectEvaluationGradeCount(@Param("categoryId") String categoryId , @Param("objectId") String objectId, @Param("gradeValue") String gradeValue, @Param("sid") long sid);

    @Select("select  evaluation_grade_value from  evaluation_grade where evaluation_category_id=  #{categoryId}  and evaluation_object_id = #{objectId} and  evaluation_grade_user_id= #{userId} and (#{sid}=null or #{sid}=sid)  limit 0,1")
    public String GetEvaluationGradeValue(@Param("categoryId") String categoryId , @Param("objectId") String objectId, @Param("userId") String userId, @Param("sid") long sid);

    @Select("SELECT c.CustomerServiceCode customerServiceCode,ifnull(e.CustomerName,s.AE002) customerName,d.name,case when a.evaluation_grade_value = 10 then 'true' ELSE 'false' END hasHelp,\n" +
            "                        a.evaluation_object_id,a.evaluation_grade_value,case when a.evaluation_grade_value = 10 then '' ELSE b.evaluation_comment_content END evaluation_comment_content ,DATE_FORMAT(a.evaluation_grade_time,'%Y-%m-%d %H:%i:%S') evaluation_comment_time\n" +
            "                        FROM evaluation_grade a \n" +
            "                        LEFT JOIN (select evaluation_object_id,evaluation_comment_user_id,max(evaluation_comment_time) evaluation_comment_time,evaluation_comment_content\n" +
            "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t from evaluation_comment\n" +
            "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t where evaluation_object_id = #{objectId}\n" +
            "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t group by evaluation_comment_user_id )\n" +
            "\t\t\t\t\t\t\t\t\t\t\t\t b ON a.evaluation_object_id = b.evaluation_object_id and a.evaluation_grade_user_id = b.evaluation_comment_user_id \n" +
            "                        LEFT JOIN mars_user c ON c.ID = a.evaluation_grade_user_id\n" +
            "                        LEFT JOIN mars_userpersonalinfo d ON d.userid = c.ID\n" +
            "                        LEFT JOIN mars_customer e ON e.CustomerCode = c.CustomerCode\n" +
            "                        LEFT JOIN serae s ON s.AE001 = c.CustomerServiceCode\n" +
            "                        WHERE a.evaluation_object_id=#{objectId} and (#{sid}=null or a.sid=#{sid})\n" +
            "                        GROUP BY a.evaluation_grade_user_id\n" +
            "                        ORDER BY a.evaluation_grade_time desc " +
            "                        limit #{start},#{size}")
    public List<EvaluationVo> getEvaluationList(@Param("objectId") String objectId , @Param("sid") long sid, @Param("start") int start, @Param("size") int size);

    @Select("select satisfysr.* ,case when count(kuqige.CaseID)>0 then true else false end as isEvaluated from satisfysr \n" +
            "inner join issue on satisfysr.caseid = issue.CrmId and satisfysr.cusid = issue.ServiceCode  and issue.IssueStatus ='Y'\n" +
            "left join kuqige on satisfysr.caseid = kuqige.CaseID and satisfysr.cusid = kuqige.CusID\n" +
            "where satisfysr.caseid =#{crmId} and satisfysr.cusid=#{serviceCode}\n" +
            "GROUP BY satisfysr.caseid")
    @Results(value = {
            @Result(property="crmId", column="caseId"),
            @Result(property="serviceCode", column="cusid"),
            @Result(property="rendnum", column="rendnum"),
            @Result(property="mainkey", column="mainkey"),
            @Result(property="isEvaluated", column="isEvaluated")})
    public SatisfyEvaluateData getEvaluationLinkInfo(@Param("crmId") String crmId,
                                                     @Param("serviceCode")String serviceCode,
                                                     @Param("userId")String userId);

    @Select("SELECT ei.evaluation_item_id, case when #{lang}='zh-CN' then ei.evaluation_item_nameCN ELSE ei.evaluation_item_nameTW END  evaluation_item_name\n" +
            "FROM evaluation_item_setting eis\n" +
            "LEFT JOIN evaluation_item ei ON ei.evaluation_item_id = eis.evaluation_item_id\n" +
            "WHERE eis.evaluation_category_id = 3\n" +
            "ORDER BY ei.evaluation_item_id asc")
    public List<Map<String,Object>> getEvaluationItems(@Param("evaluation_category_id") int evaluation_category_id,
                                                        @Param("lang") String lang);

    /*@Select("SELECT COUNT(*) FROM evaluation_grade a \n" +
            "LEFT JOIN mars_user c ON c.ID = a.evaluation_grade_user_id \n" +
            "LEFT JOIN mars_userpersonalinfo d ON d.userid = c.ID \n" +
            "LEFT JOIN mars_customer e ON e.CustomerCode = c.CustomerCode \n" +
            "LEFT JOIN serae s ON s.AE001 = c.CustomerServiceCode \n" +
            "WHERE  a.evaluation_object_id=#{objectId} and (#{sid}=null or a.sid=#{sid}) ")
    public int getEvaluationListCount(@Param("objectId") String objectId , @Param("sid") long sid);*/

}
