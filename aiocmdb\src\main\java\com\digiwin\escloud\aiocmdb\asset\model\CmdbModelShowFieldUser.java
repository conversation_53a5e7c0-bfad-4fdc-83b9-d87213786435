package com.digiwin.escloud.aiocmdb.asset.model;

import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 资产里面 模型显示的列字段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@ApiModel(value = "CmdbModelShowFieldUser对象", description = "资产里面 模型显示的列字段")
public class CmdbModelShowFieldUser implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long sid;

    @ApiModelProperty("模型编码")
    private String modelCode;

    private String userId;

    @ApiModelProperty("字段编码")
    private String fieldCode;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("字段所属模型字段分组")
    private String modelFieldGroupCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getModelFieldGroupCode() {
        return modelFieldGroupCode;
    }

    public void setModelFieldGroupCode(String modelFieldGroupCode) {
        this.modelFieldGroupCode = modelFieldGroupCode;
    }

    @Override
    public String toString() {
        return "CmdbModelShowFieldUser{" +
            "id = " + id +
            ", sid = " + sid +
            ", modelCode = " + modelCode +
            ", userId = " + userId +
            ", fieldCode = " + fieldCode +
            ", sort = " + sort +
            ", modelFieldGroupCode = " + modelFieldGroupCode +
        "}";
    }
}
